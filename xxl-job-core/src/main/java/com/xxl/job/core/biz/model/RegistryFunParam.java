package com.xxl.job.core.biz.model;

import java.io.Serializable;

/**
 */
public class RegistryFunParam implements Serializable {
    private static final long serialVersionUID = 42L;

    private String registryGroup;

    /**
     * 就是xxljob里面的value （任务名称）
     */
    private String executorHandler;

    /**
     * 任务执行规则
     */
    String cron;

    /**
     * 是否启用 默认开启
     */
    boolean enabled;

    /**
     * 任务描述
     */
    String desc;

    /**
     * 路由策略
     * see ExecutorRouteStrategyEnum
     */
    String executorRouteStrategyEnum;
    /**
     * Glue类型
     * see GlueTypeEnum
     */
    String glueTypeEnum;
    /**
     * 阻塞处理策略
     * see ExecutorBlockStrategyEnum
     */
    String executorBlockStrategyEnum;
    /**
     * 调度类型
     * see ScheduleTypeEnum
     */
    String scheduleTypeEnum;
    /**
     * 调度过期策略
     * see MisfireStrategyEnum
     */
    String misfireStrategyEnum;

    public String getExecutorHandler() {
        return executorHandler;
    }

    public void setExecutorHandler(String executorHandler) {
        this.executorHandler = executorHandler;
    }

    public String getRegistryGroup() {
        return registryGroup;
    }

    public void setRegistryGroup(String registryGroup) {
        this.registryGroup = registryGroup;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getExecutorRouteStrategyEnum() {
        return executorRouteStrategyEnum;
    }

    public void setExecutorRouteStrategyEnum(String executorRouteStrategyEnum) {
        this.executorRouteStrategyEnum = executorRouteStrategyEnum;
    }

    public String getGlueTypeEnum() {
        return glueTypeEnum;
    }

    public void setGlueTypeEnum(String glueTypeEnum) {
        this.glueTypeEnum = glueTypeEnum;
    }

    public String getExecutorBlockStrategyEnum() {
        return executorBlockStrategyEnum;
    }

    public void setExecutorBlockStrategyEnum(String executorBlockStrategyEnum) {
        this.executorBlockStrategyEnum = executorBlockStrategyEnum;
    }

    public String getScheduleTypeEnum() {
        return scheduleTypeEnum;
    }

    public void setScheduleTypeEnum(String scheduleTypeEnum) {
        this.scheduleTypeEnum = scheduleTypeEnum;
    }

    public String getMisfireStrategyEnum() {
        return misfireStrategyEnum;
    }

    public void setMisfireStrategyEnum(String misfireStrategyEnum) {
        this.misfireStrategyEnum = misfireStrategyEnum;
    }


    @Override
    public String toString() {
        return "RegistryFunParam{" +
                "registryGroup='" + registryGroup + '\'' +
                ", executorHandler='" + executorHandler + '\'' +
                ", cron='" + cron + '\'' +
                ", enabled=" + enabled +
                ", desc='" + desc + '\'' +
                ", executorRouteStrategyEnum='" + executorRouteStrategyEnum + '\'' +
                ", glueTypeEnum='" + glueTypeEnum + '\'' +
                ", executorBlockStrategyEnum='" + executorBlockStrategyEnum + '\'' +
                ", scheduleTypeEnum='" + scheduleTypeEnum + '\'' +
                ", misfireStrategyEnum='" + misfireStrategyEnum + '\'' +
                '}';
    }
}
