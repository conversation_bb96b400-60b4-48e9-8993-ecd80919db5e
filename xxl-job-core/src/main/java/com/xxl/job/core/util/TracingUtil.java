package com.xxl.job.core.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;

/**
 * 链路追踪工具类
 * 支持可选的 Tracer 注入，当 Tracer 可用时自动开启链路追踪
 * 
 * <AUTHOR>
 */
public class TracingUtil {
    private static final Logger logger = LoggerFactory.getLogger(TracingUtil.class);
    
    private static Object tracer;
    private static Method nextSpanMethod;
    private static Method nameMethod;
    private static Method tagMethod;
    private static Method startMethod;
    private static Method endMethod;
    private static Method closeMethod;
    private static boolean tracingEnabled = false;
    
    static {
        initTracing();
    }
    
    /**
     * 初始化链路追踪
     */
    private static void initTracing() {
        try {
            // 尝试通过反射获取 Tracer 实例
            // 这里假设使用 Spring Cloud Sleuth 或类似的链路追踪框架
            Class<?> tracerClass = Class.forName("brave.Tracer");
            
            // 尝试从 Spring 上下文获取 Tracer Bean
            try {
                Class<?> applicationContextClass = Class.forName("org.springframework.context.ApplicationContext");
                Class<?> applicationContextAwareClass = Class.forName("org.springframework.context.ApplicationContextAware");
                Class<?> contextHolderClass = Class.forName("org.springframework.context.ApplicationContextHolder");
                
                Method getApplicationContextMethod = contextHolderClass.getMethod("getApplicationContext");
                Object applicationContext = getApplicationContextMethod.invoke(null);
                
                if (applicationContext != null) {
                    Method getBeanMethod = applicationContextClass.getMethod("getBean", Class.class);
                    tracer = getBeanMethod.invoke(applicationContext, tracerClass);
                }
            } catch (Exception e) {
                logger.debug("Failed to get Tracer from Spring context, trying other methods", e);
            }
            
            if (tracer != null) {
                // 获取 Tracer 的相关方法
                nextSpanMethod = tracerClass.getMethod("nextSpan");
                
                // 获取 Span 相关方法
                Class<?> spanClass = Class.forName("brave.Span");
                nameMethod = spanClass.getMethod("name", String.class);
                tagMethod = spanClass.getMethod("tag", String.class, String.class);
                startMethod = spanClass.getMethod("start");
                endMethod = spanClass.getMethod("end");
                
                // 获取 SpanInScope 相关方法
                Class<?> spanInScopeClass = Class.forName("brave.Span$SpanInScope");
                closeMethod = spanInScopeClass.getMethod("close");
                
                tracingEnabled = true;
                logger.info(">>>>>>>>>>> xxl-job tracing initialized successfully");
            }
        } catch (Exception e) {
            logger.debug("Tracing not available, continuing without tracing support", e);
            tracingEnabled = false;
        }
    }
    
    /**
     * 检查链路追踪是否可用
     */
    public static boolean isTracingEnabled() {
        return tracingEnabled;
    }
    
    /**
     * 开始一个新的 Span
     * 
     * @param spanName Span 名称
     * @return TracingContext 对象，用于后续操作
     */
    public static TracingContext startSpan(String spanName) {
        if (!tracingEnabled) {
            return new TracingContext(null, null);
        }
        
        try {
            // 创建新的 Span
            Object span = nextSpanMethod.invoke(tracer);
            nameMethod.invoke(span, spanName);
            
            // 启动 Span 并获取 SpanInScope
            Object spanInScope = startMethod.invoke(span);
            
            return new TracingContext(span, spanInScope);
        } catch (Exception e) {
            logger.warn("Failed to start tracing span: " + spanName, e);
            return new TracingContext(null, null);
        }
    }
    
    /**
     * 为 Span 添加标签
     * 
     * @param context TracingContext
     * @param key 标签键
     * @param value 标签值
     */
    public static void addTag(TracingContext context, String key, String value) {
        if (!tracingEnabled || context == null || context.span == null) {
            return;
        }
        
        try {
            tagMethod.invoke(context.span, key, value);
        } catch (Exception e) {
            logger.warn("Failed to add tag to span: " + key + "=" + value, e);
        }
    }
    
    /**
     * 结束 Span
     * 
     * @param context TracingContext
     */
    public static void endSpan(TracingContext context) {
        if (!tracingEnabled || context == null) {
            return;
        }
        
        try {
            // 关闭 SpanInScope
            if (context.spanInScope != null) {
                closeMethod.invoke(context.spanInScope);
            }
            
            // 结束 Span
            if (context.span != null) {
                endMethod.invoke(context.span);
            }
        } catch (Exception e) {
            logger.warn("Failed to end tracing span", e);
        }
    }
    
    /**
     * 链路追踪上下文
     */
    public static class TracingContext {
        private final Object span;
        private final Object spanInScope;
        
        public TracingContext(Object span, Object spanInScope) {
            this.span = span;
            this.spanInScope = spanInScope;
        }
        
        public boolean isValid() {
            return span != null;
        }
    }
    
    /**
     * 执行带链路追踪的操作
     * 
     * @param spanName Span 名称
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     * @throws Exception 操作异常
     */
    public static <T> T executeWithTracing(String spanName, TracingOperation<T> operation) throws Exception {
        TracingContext context = startSpan(spanName);
        try {
            return operation.execute(context);
        } finally {
            endSpan(context);
        }
    }
    
    /**
     * 执行带链路追踪的操作（无返回值）
     * 
     * @param spanName Span 名称
     * @param operation 要执行的操作
     * @throws Exception 操作异常
     */
    public static void executeWithTracing(String spanName, TracingVoidOperation operation) throws Exception {
        TracingContext context = startSpan(spanName);
        try {
            operation.execute(context);
        } finally {
            endSpan(context);
        }
    }
    
    /**
     * 链路追踪操作接口
     */
    @FunctionalInterface
    public interface TracingOperation<T> {
        T execute(TracingContext context) throws Exception;
    }
    
    /**
     * 链路追踪操作接口（无返回值）
     */
    @FunctionalInterface
    public interface TracingVoidOperation {
        void execute(TracingContext context) throws Exception;
    }
}
