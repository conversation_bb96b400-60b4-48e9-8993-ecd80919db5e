package com.xxl.job.core.handler.annotation;

import java.lang.annotation.*;

/**
 * Vlinker 自定义xxl job
 * 实现自动注册
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface VlinkerXxlJob {

    /**
     * 任务名称 appname下唯一
     */
    String value();

    /**
     * 任务执行规则
     */
    String cron();

    /**
     * 是否启用 默认开启
     */
    boolean enabled() default true;

    /**
     * 任务描述
     */
    String desc() default "";

    /**
     * 路由策略
     * see ExecutorRouteStrategyEnum
     */
    String executorRouteStrategyEnum() default "FIRST";

    /**
     * Glue类型
     * see GlueTypeEnum
     */
    String glueTypeEnum() default "BEAN";

    /**
     * 阻塞处理策略
     * see ExecutorBlockStrategyEnum
     */
    String executorBlockStrategyEnum() default "SERIAL_EXECUTION";

    /**
     * 调度类型
     * see ScheduleTypeEnum
     */
    String scheduleTypeEnum() default "CRON";

    /**
     * 调度过期策略
     * see MisfireStrategyEnum
     */
    String misfireStrategyEnum() default "DO_NOTHING";

    /**
     * init handler, invoked when JobThread init
     */
    String init() default "";

    /**
     * destroy handler, invoked when JobThread destroy
     */
    String destroy() default "";

}
