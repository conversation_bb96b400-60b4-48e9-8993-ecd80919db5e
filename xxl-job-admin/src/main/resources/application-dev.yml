### web
server:
  port: 12003
  servlet:
    context-path: /xxl-job-admin

  ### actuator
management:
  server:
    servlet:
      context-path: /actuator
    base-path: /actuator
  health:
    mail:
      enabled: false

  ### resources
spring:
  mvc:
    servlet:
      load-on-startup: 0
    static-path-pattern: /static/**
  ### freemarker
  web:
    resources:
      static-locations: classpath:/static/

  freemarker:
    templateLoaderPath: classpath:/templates/
    suffix: .ftl
    charset: UTF-8
    request-context-attribute: request
    settings:
      number_format: 0.##########


  ### xxl-job, datasource
  datasource:
#    url: jdbc:dm://10.1.1.16:5236/xxl_job_demo?characterEncoding=utf8&allowPublicKeyRetrieval=true&useSSL=false&serverTimezone=GMT%2B8
#    username: dmadmin
#    password: dmaDmin_456
#    driver-class-name: dm.jdbc.driver.DmDriver

    url: ************************************************************************************************************************************
    username: zhangjinchao
    password: GUHv4VrTF9dABsxQuaS7
    driver-class-name: com.mysql.cj.jdbc.Driver

  ### datasource-pool
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 2
      maximum-pool-size: 5
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 900000
      connection-timeout: 10000
      connection-test-query: SELECT 1
      validation-timeout: 1000

  ### xxl-job, email
  mail:
    host: smtp.qq.com
    port: 25
    username: <EMAIL>
    from: <EMAIL>
    password: xxx
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory



### mybatis
mybatis:
  mapper-locations: classpath:/mybatis-mapper/*Mapper.xml
  #mybatis.type-aliases-package=com.xxl.job.admin.core.model
  ### xxl-job, access token
xxl:
  job:
    accessToken: default_token
    timeout: 1000
  ### xxl-job, i18n (default is zh_CN, and you can choose "zh_CN", "zh_TC" and "en")
    i18n: zh_CN
  ## xxl-job, triggerpool max size
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
  ### xxl-job, log retention days
    logretentiondays: 30
#xxl.job.enabled=true
#xxl.job.admin.addresses=http://127.0.0.1:8080/xxl-job-admin
#xxl.job.executor.address=
#xxl.job.executor.ip=
#xxl.job.executor.appname=xxl-job-executor-manage
#xxl.job.executor.port=9999
#xxl.job.executor.logpath=logs/console
#xxl.job.executor.logretentiondays=7