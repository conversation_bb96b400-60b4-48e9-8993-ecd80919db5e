<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.saida</groupId>
	<artifactId>xxl-job</artifactId>
	<version>2.0.2</version>
	<packaging>pom</packaging>

	<name>${project.artifactId}</name>
	<modules>
		<module>xxl-job-core</module>
		<module>xxl-job-admin</module>
    </modules>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<maven.test.skip>true</maven.test.skip>

		<netty.version>4.1.90.Final</netty.version>
		<gson.version>2.10.1</gson.version>

		<spring.version>5.3.26</spring.version>
		<spring-boot.version>2.3.2.RELEASE</spring-boot.version>

		<mybatis-spring-boot-starter.version>2.3.0</mybatis-spring-boot-starter.version>
		<mysql-connector-j.version>8.0.32</mysql-connector-j.version>

		<slf4j-api.version>1.7.36</slf4j-api.version>
		<junit-jupiter.version>5.9.2</junit-jupiter.version>
		<javax.annotation-api.version>1.3.2</javax.annotation-api.version>

		<groovy.version>4.0.10</groovy.version>

		<maven-source-plugin.version>3.2.1</maven-source-plugin.version>
		<maven-javadoc-plugin.version>3.5.0</maven-javadoc-plugin.version>
		<maven-gpg-plugin.version>3.0.1</maven-gpg-plugin.version>
	</properties>

	<distributionManagement>
		<repository>
			<id>nexus-maven</id>
			<url>https://mvn.sdvideo.cn/repository/maven-releases/</url>
			<name>Release Repository</name>
		</repository>
	</distributionManagement>

	<build>
		<plugins>
			<!--源码打包配置-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.1.2</version>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>